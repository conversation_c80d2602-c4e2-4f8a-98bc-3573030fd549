#!/usr/bin/env python3
"""
Create an academic-style Gantt chart that matches the format of the example charts
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.patches import Rectangle
import pandas as pd

def create_academic_gantt_chart():
    """Create an academic-style Gantt chart matching the example format"""
    
    # Create figure with specific dimensions to match academic style
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Define work packages and activities with their timelines
    activities = [
        # WP0 Activities
        {'name': 'Project initiation and setup', 'wp': 'WP0', 'start': 1, 'duration': 2, 'color': '#E8F4FD'},
        {'name': 'Data acquisition (RO, ERA5, CMIP)', 'wp': 'WP0', 'start': 1, 'duration': 4, 'color': '#E8F4FD'},
        {'name': 'Analysis environment setup', 'wp': 'WP0', 'start': 2, 'duration': 2, 'color': '#E8F4FD'},
        {'name': 'Career Development Plan (D0.1)', 'wp': 'WP0', 'start': 2, 'duration': 2, 'color': '#E8F4FD'},
        {'name': 'Data Management Plan (D0.2)', 'wp': 'WP0', 'start': 4, 'duration': 3, 'color': '#E8F4FD'},
        
        # WP1 Activities
        {'name': 'RO data preprocessing and quality control', 'wp': 'WP1', 'start': 3, 'duration': 3, 'color': '#D4E6F1'},
        {'name': 'GW parameter extraction methodology', 'wp': 'WP1', 'start': 4, 'duration': 4, 'color': '#D4E6F1'},
        {'name': 'Validation with TIMED/SABER and Aura/MLS', 'wp': 'WP1', 'start': 6, 'duration': 4, 'color': '#D4E6F1'},
        {'name': 'Case study analysis (August 2019 event)', 'wp': 'WP1', 'start': 7, 'duration': 3, 'color': '#D4E6F1'},
        {'name': 'Global GW climatology development', 'wp': 'WP1', 'start': 9, 'duration': 4, 'color': '#D4E6F1'},
        {'name': 'Validation report preparation (D1.1)', 'wp': 'WP1', 'start': 11, 'duration': 2, 'color': '#D4E6F1'},
        
        # WP2 Activities
        {'name': 'MJO event categorization', 'wp': 'WP2', 'start': 10, 'duration': 3, 'color': '#A9CCE3'},
        {'name': 'MJO-GW composite analysis', 'wp': 'WP2', 'start': 12, 'duration': 4, 'color': '#A9CCE3'},
        {'name': 'QBO and ENSO interaction analysis', 'wp': 'WP2', 'start': 14, 'duration': 4, 'color': '#A9CCE3'},
        {'name': 'Global climatology synthesis', 'wp': 'WP2', 'start': 16, 'duration': 3, 'color': '#A9CCE3'},
        {'name': 'MJO-GW climatology report (D2.1)', 'wp': 'WP2', 'start': 17, 'duration': 2, 'color': '#A9CCE3'},
        
        # WP3 Activities
        {'name': 'CMIP6/7 data acquisition and processing', 'wp': 'WP3', 'start': 16, 'duration': 3, 'color': '#7FB3D3'},
        {'name': 'Model-observation comparison framework', 'wp': 'WP3', 'start': 17, 'duration': 3, 'color': '#7FB3D3'},
        {'name': 'Systematic bias identification', 'wp': 'WP3', 'start': 19, 'duration': 3, 'color': '#7FB3D3'},
        {'name': 'Model evaluation metrics application', 'wp': 'WP3', 'start': 20, 'duration': 3, 'color': '#7FB3D3'},
        {'name': 'Model evaluation report (D3.1)', 'wp': 'WP3', 'start': 21, 'duration': 2, 'color': '#7FB3D3'},
        
        # WP4 Activities
        {'name': 'Conference presentations (ongoing)', 'wp': 'WP4', 'start': 6, 'duration': 19, 'color': '#5499C7'},
        {'name': 'First manuscript preparation', 'wp': 'WP4', 'start': 12, 'duration': 4, 'color': '#5499C7'},
        {'name': 'Second manuscript preparation', 'wp': 'WP4', 'start': 18, 'duration': 4, 'color': '#5499C7'},
        {'name': 'Third manuscript preparation', 'wp': 'WP4', 'start': 22, 'duration': 3, 'color': '#5499C7'},
        {'name': 'Data archiving and documentation', 'wp': 'WP4', 'start': 20, 'duration': 5, 'color': '#5499C7'},
        {'name': 'Final project report (D4.1)', 'wp': 'WP4', 'start': 23, 'duration': 2, 'color': '#5499C7'},
    ]
    
    # Define deliverables
    deliverables = [
        {'name': 'D0.1', 'month': 3, 'description': 'Career Development Plan'},
        {'name': 'D0.2', 'month': 6, 'description': 'Data Management Plan'},
        {'name': 'D1.1', 'month': 12, 'description': 'Validation Report & GW Climatology'},
        {'name': 'D2.1', 'month': 18, 'description': 'MJO-GW Climatology Report'},
        {'name': 'D3.1', 'month': 22, 'description': 'Model Evaluation Report'},
        {'name': 'D4.1', 'month': 24, 'description': 'Final Project Report'}
    ]
    
    # Set up the chart dimensions
    y_positions = np.arange(len(activities))
    
    # Create the Gantt bars
    for i, activity in enumerate(activities):
        # Create rectangle for each activity
        rect = Rectangle((activity['start']-1, i-0.4), activity['duration'], 0.8, 
                        facecolor=activity['color'], edgecolor='black', linewidth=0.5)
        ax.add_patch(rect)
        
        # Add activity name inside or next to the bar
        text_x = activity['start'] - 1 + activity['duration']/2
        if activity['duration'] > 3:  # If bar is long enough, put text inside
            ax.text(text_x, i, activity['name'], ha='center', va='center', 
                   fontsize=8, fontweight='normal', wrap=True)
        else:  # If bar is short, put text to the right
            ax.text(activity['start'] + activity['duration'], i, activity['name'], 
                   ha='left', va='center', fontsize=8, fontweight='normal')
    
    # Add deliverable markers
    for deliv in deliverables:
        # Find the appropriate y-position (middle of the chart)
        y_pos = len(activities) + 1
        
        # Add diamond marker
        ax.scatter(deliv['month'], y_pos, s=100, marker='D', 
                  color='red', edgecolor='black', linewidth=1, zorder=10)
        
        # Add deliverable label
        ax.text(deliv['month'], y_pos + 0.5, deliv['name'], 
               ha='center', va='bottom', fontsize=9, fontweight='bold', color='red')
    
    # Customize the chart
    ax.set_xlim(0, 25)
    ax.set_ylim(-1, len(activities) + 3)
    
    # Set labels and title
    ax.set_xlabel('Project Timeline (Months)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Work Packages and Activities', fontsize=12, fontweight='bold')
    ax.set_title('MJO Stratospheric Gravity Waves Project\n24-Month Work Plan and Timeline', 
                fontsize=14, fontweight='bold', pad=20)
    
    # Set y-axis labels
    ax.set_yticks(y_positions)
    activity_labels = []
    for activity in activities:
        label = f"{activity['wp']}: {activity['name']}"
        if len(label) > 50:
            label = label[:47] + "..."
        activity_labels.append(label)
    ax.set_yticklabels(activity_labels, fontsize=8)
    
    # Set x-axis ticks for months
    months = np.arange(1, 25)
    ax.set_xticks(months)
    ax.set_xticklabels([f'M{m}' for m in months], rotation=0, fontsize=9)
    
    # Add grid
    ax.grid(True, axis='x', alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # Add quarter separators
    quarters = [6, 12, 18, 24]
    for q in quarters:
        ax.axvline(x=q, color='red', linestyle='--', alpha=0.7, linewidth=1.5)
        ax.text(q, len(activities) + 2.5, f'Q{q//6}', ha='center', va='center', 
                fontweight='bold', color='red', fontsize=10,
                bbox=dict(boxstyle='round,pad=0.2', facecolor='white', edgecolor='red'))
    
    # Add work package legend
    wp_colors = {
        'WP0': '#E8F4FD',
        'WP1': '#D4E6F1', 
        'WP2': '#A9CCE3',
        'WP3': '#7FB3D3',
        'WP4': '#5499C7'
    }
    
    wp_descriptions = {
        'WP0': 'Project Management & Setup',
        'WP1': 'Validation & GW Parameter Climatology',
        'WP2': 'MJO-Modulated GW Activity Analysis',
        'WP3': 'Climate Model Evaluation',
        'WP4': 'Dissemination & Reporting'
    }
    
    legend_elements = []
    for wp, color in wp_colors.items():
        legend_elements.append(patches.Patch(color=color, label=f'{wp}: {wp_descriptions[wp]}'))
    
    ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0.02, 0.98),
             fontsize=9, title='Work Packages', title_fontsize=10)
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the chart
    plt.savefig('MJO_Academic_Gantt_Chart.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('MJO_Academic_Gantt_Chart.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print("Academic-style Gantt chart saved as 'MJO_Academic_Gantt_Chart.png' and 'MJO_Academic_Gantt_Chart.pdf'")
    
    return fig, ax

def create_simple_table_gantt():
    """Create a simple table-style Gantt chart"""
    
    # Create figure
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # Define simplified work packages
    work_packages = [
        {'name': 'WP0: Project Management & Setup', 'start': 1, 'end': 6, 'color': '#E3F2FD'},
        {'name': 'WP1: Validation & GW Parameter Climatology', 'start': 3, 'end': 12, 'color': '#BBDEFB'},
        {'name': 'WP2: MJO-Modulated GW Activity Analysis', 'start': 10, 'end': 18, 'color': '#90CAF9'},
        {'name': 'WP3: Climate Model Evaluation', 'start': 16, 'end': 22, 'color': '#64B5F6'},
        {'name': 'WP4: Dissemination & Reporting', 'start': 1, 'end': 24, 'color': '#42A5F5'},
    ]
    
    # Create table-like structure
    y_positions = np.arange(len(work_packages))
    
    # Draw work package bars
    for i, wp in enumerate(work_packages):
        # Main bar
        rect = Rectangle((wp['start']-1, i-0.3), wp['end']-wp['start']+1, 0.6, 
                        facecolor=wp['color'], edgecolor='black', linewidth=1)
        ax.add_patch(rect)
        
        # Add work package name
        ax.text(-0.5, i, wp['name'], ha='right', va='center', 
               fontsize=11, fontweight='bold')
    
    # Add deliverable markers
    deliverables = [
        {'month': 3, 'name': 'D0.1'},
        {'month': 6, 'name': 'D0.2'},
        {'month': 12, 'name': 'D1.1'},
        {'month': 18, 'name': 'D2.1'},
        {'month': 22, 'name': 'D3.1'},
        {'month': 24, 'name': 'D4.1'}
    ]
    
    for deliv in deliverables:
        # Add vertical line for deliverable
        ax.axvline(x=deliv['month'], color='red', linestyle='-', alpha=0.8, linewidth=2)
        
        # Add deliverable label at top
        ax.text(deliv['month'], len(work_packages), deliv['name'], 
               ha='center', va='bottom', fontsize=10, fontweight='bold', color='red',
               bbox=dict(boxstyle='round,pad=0.2', facecolor='white', edgecolor='red'))
    
    # Customize the chart
    ax.set_xlim(-8, 25)
    ax.set_ylim(-0.5, len(work_packages) + 1)
    
    # Set labels and title
    ax.set_xlabel('Project Timeline (Months)', fontsize=14, fontweight='bold')
    ax.set_title('MJO Stratospheric Gravity Waves Project - 24 Month Timeline', 
                fontsize=16, fontweight='bold', pad=20)
    
    # Remove y-axis labels and ticks
    ax.set_yticks([])
    
    # Set x-axis ticks for months
    months = np.arange(1, 25)
    ax.set_xticks(months)
    ax.set_xticklabels([f'{m}' for m in months], fontsize=10)
    
    # Add grid
    ax.grid(True, axis='x', alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # Add quarter markers
    quarters = [6, 12, 18, 24]
    for q in quarters:
        ax.axvline(x=q, color='blue', linestyle='--', alpha=0.5, linewidth=1)
    
    # Remove top and right spines
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_visible(False)
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the chart
    plt.savefig('MJO_Simple_Gantt_Chart.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('MJO_Simple_Gantt_Chart.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print("Simple Gantt chart saved as 'MJO_Simple_Gantt_Chart.png' and 'MJO_Simple_Gantt_Chart.pdf'")
    
    return fig, ax

if __name__ == "__main__":
    # Create both chart styles
    print("Creating academic-style Gantt chart...")
    fig1, ax1 = create_academic_gantt_chart()
    
    print("Creating simple table-style Gantt chart...")
    fig2, ax2 = create_simple_table_gantt()
    
    # Display the charts
    plt.show()
    
    print("\nBoth Gantt chart styles have been created!")
    print("Files generated:")
    print("- MJO_Academic_Gantt_Chart.png/pdf")
    print("- MJO_Simple_Gantt_Chart.png/pdf")
