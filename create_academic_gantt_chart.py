#!/usr/bin/env python3
"""
Create an academic-style Gantt chart that matches the format of the example charts
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.patches import Rectangle
import pandas as pd

def create_academic_gantt_chart():
    """Create an academic-style Gantt chart matching the example format"""

    # Create figure with wider, shorter dimensions
    fig, ax = plt.subplots(figsize=(18, 6))

    # Define work packages and activities with codes instead of full descriptions
    activities = [
        # WP0 Activities
        {'code': 'WP0.1', 'wp': 'WP0', 'start': 1, 'duration': 2, 'color': '#E3F2FD'},
        {'code': 'WP0.2', 'wp': 'WP0', 'start': 1, 'duration': 4, 'color': '#E3F2FD'},
        {'code': 'WP0.3', 'wp': 'WP0', 'start': 2, 'duration': 2, 'color': '#E3F2FD'},
        {'code': 'WP0.4', 'wp': 'WP0', 'start': 2, 'duration': 2, 'color': '#E3F2FD'},
        {'code': 'WP0.5', 'wp': 'WP0', 'start': 4, 'duration': 3, 'color': '#E3F2FD'},

        # WP1 Activities
        {'code': 'WP1.1', 'wp': 'WP1', 'start': 3, 'duration': 3, 'color': '#BBDEFB'},
        {'code': 'WP1.2', 'wp': 'WP1', 'start': 4, 'duration': 4, 'color': '#BBDEFB'},
        {'code': 'WP1.3', 'wp': 'WP1', 'start': 6, 'duration': 4, 'color': '#BBDEFB'},
        {'code': 'WP1.4', 'wp': 'WP1', 'start': 7, 'duration': 3, 'color': '#BBDEFB'},
        {'code': 'WP1.5', 'wp': 'WP1', 'start': 9, 'duration': 4, 'color': '#BBDEFB'},
        {'code': 'WP1.6', 'wp': 'WP1', 'start': 11, 'duration': 2, 'color': '#BBDEFB'},

        # WP2 Activities
        {'code': 'WP2.1', 'wp': 'WP2', 'start': 10, 'duration': 3, 'color': '#90CAF9'},
        {'code': 'WP2.2', 'wp': 'WP2', 'start': 12, 'duration': 4, 'color': '#90CAF9'},
        {'code': 'WP2.3', 'wp': 'WP2', 'start': 14, 'duration': 4, 'color': '#90CAF9'},
        {'code': 'WP2.4', 'wp': 'WP2', 'start': 16, 'duration': 3, 'color': '#90CAF9'},
        {'code': 'WP2.5', 'wp': 'WP2', 'start': 17, 'duration': 2, 'color': '#90CAF9'},

        # WP3 Activities
        {'code': 'WP3.1', 'wp': 'WP3', 'start': 16, 'duration': 3, 'color': '#64B5F6'},
        {'code': 'WP3.2', 'wp': 'WP3', 'start': 17, 'duration': 3, 'color': '#64B5F6'},
        {'code': 'WP3.3', 'wp': 'WP3', 'start': 19, 'duration': 3, 'color': '#64B5F6'},
        {'code': 'WP3.4', 'wp': 'WP3', 'start': 20, 'duration': 3, 'color': '#64B5F6'},
        {'code': 'WP3.5', 'wp': 'WP3', 'start': 21, 'duration': 2, 'color': '#64B5F6'},

        # WP4 Activities
        {'code': 'WP4.1', 'wp': 'WP4', 'start': 6, 'duration': 19, 'color': '#42A5F5'},
        {'code': 'WP4.2', 'wp': 'WP4', 'start': 12, 'duration': 4, 'color': '#42A5F5'},
        {'code': 'WP4.3', 'wp': 'WP4', 'start': 18, 'duration': 4, 'color': '#42A5F5'},
        {'code': 'WP4.4', 'wp': 'WP4', 'start': 22, 'duration': 3, 'color': '#42A5F5'},
        {'code': 'WP4.5', 'wp': 'WP4', 'start': 20, 'duration': 5, 'color': '#42A5F5'},
        {'code': 'WP4.6', 'wp': 'WP4', 'start': 23, 'duration': 2, 'color': '#42A5F5'},
    ]
    
    # Define deliverables
    deliverables = [
        {'name': 'D0.1', 'month': 3, 'description': 'Career Development Plan'},
        {'name': 'D0.2', 'month': 6, 'description': 'Data Management Plan'},
        {'name': 'D1.1', 'month': 12, 'description': 'Validation Report & GW Climatology'},
        {'name': 'D2.1', 'month': 18, 'description': 'MJO-GW Climatology Report'},
        {'name': 'D3.1', 'month': 22, 'description': 'Model Evaluation Report'},
        {'name': 'D4.1', 'month': 24, 'description': 'Final Project Report'}
    ]
    
    # Set up the chart dimensions
    y_positions = np.arange(len(activities))
    
    # Create the Gantt bars
    for i, activity in enumerate(activities):
        # Create rectangle for each activity
        rect = Rectangle((activity['start']-1, i-0.35), activity['duration'], 0.7,
                        facecolor=activity['color'], edgecolor='black', linewidth=0.8)
        ax.add_patch(rect)

        # Add activity code inside the bar
        text_x = activity['start'] - 1 + activity['duration']/2
        ax.text(text_x, i, activity['code'], ha='center', va='center',
               fontsize=10, fontweight='bold', color='black')

    # Add deliverable markers at the bottom
    for deliv in deliverables:
        # Add diamond marker at bottom of chart
        y_pos = -0.8

        # Add diamond marker
        ax.scatter(deliv['month'], y_pos, s=120, marker='D',
                  color='red', edgecolor='black', linewidth=1, zorder=10)

        # Add deliverable label
        ax.text(deliv['month'], y_pos - 0.3, deliv['name'],
               ha='center', va='top', fontsize=10, fontweight='bold', color='red')

    # Customize the chart
    ax.set_xlim(0, 25)
    ax.set_ylim(-1.5, len(activities) + 0.5)

    # Set labels and title
    ax.set_xlabel('')  # Remove x-label since timeline is at top
    ax.set_ylabel('Work Packages', fontsize=14, fontweight='bold')
    ax.set_title('MJO Stratospheric Gravity Waves Project - 24 Month Timeline',
                fontsize=16, fontweight='bold', pad=30)

    # Set y-axis labels with just activity codes
    ax.set_yticks(y_positions)
    activity_labels = [activity['code'] for activity in activities]
    ax.set_yticklabels(activity_labels, fontsize=11, fontweight='bold')

    # Set x-axis ticks for months at TOP
    months = np.arange(1, 25)
    ax.set_xticks(months)
    ax.set_xticklabels([f'M{m}' for m in months], rotation=0, fontsize=10)
    ax.xaxis.tick_top()  # Move x-axis to top
    ax.xaxis.set_label_position('top')
    ax.set_xlabel('Project Timeline (Months)', fontsize=14, fontweight='bold')

    # Add grid
    ax.grid(True, axis='x', alpha=0.4, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # Add quarter separators
    quarters = [6, 12, 18, 24]
    for q in quarters:
        ax.axvline(x=q, color='darkblue', linestyle='--', alpha=0.6, linewidth=2)
        ax.text(q, len(activities) + 0.2, f'Q{q//6}', ha='center', va='bottom',
                fontweight='bold', color='darkblue', fontsize=11,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', edgecolor='darkblue'))

    # Add work package legend with improved colors
    wp_colors = {
        'WP0': '#E3F2FD',
        'WP1': '#BBDEFB',
        'WP2': '#90CAF9',
        'WP3': '#64B5F6',
        'WP4': '#42A5F5'
    }

    wp_descriptions = {
        'WP0': 'Project Management & Setup',
        'WP1': 'Validation & GW Parameter Climatology',
        'WP2': 'MJO-Modulated GW Activity Analysis',
        'WP3': 'Climate Model Evaluation',
        'WP4': 'Dissemination & Reporting'
    }

    legend_elements = []
    for wp, color in wp_colors.items():
        legend_elements.append(patches.Patch(color=color, label=f'{wp}: {wp_descriptions[wp]}'))

    ax.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.02, 0.02),
             fontsize=10, title='Work Packages', title_fontsize=11, frameon=True,
             fancybox=True, shadow=True)

    # Remove top and right spines for cleaner look
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['bottom'].set_visible(False)

    # Adjust layout
    plt.tight_layout()

    # Save the chart
    plt.savefig('MJO_Academic_Gantt_Chart.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.savefig('MJO_Academic_Gantt_Chart.pdf', bbox_inches='tight',
                facecolor='white', edgecolor='none')

    print("Academic-style Gantt chart saved as 'MJO_Academic_Gantt_Chart.png' and 'MJO_Academic_Gantt_Chart.pdf'")

    return fig, ax

def create_simple_table_gantt():
    """Create a simple table-style Gantt chart"""

    # Create figure with wider, shorter dimensions
    fig, ax = plt.subplots(figsize=(18, 4))

    # Define simplified work packages with codes
    work_packages = [
        {'code': 'WP0', 'name': 'Project Management & Setup', 'start': 1, 'end': 6, 'color': '#E3F2FD'},
        {'code': 'WP1', 'name': 'Validation & GW Parameter Climatology', 'start': 3, 'end': 12, 'color': '#BBDEFB'},
        {'code': 'WP2', 'name': 'MJO-Modulated GW Activity Analysis', 'start': 10, 'end': 18, 'color': '#90CAF9'},
        {'code': 'WP3', 'name': 'Climate Model Evaluation', 'start': 16, 'end': 22, 'color': '#64B5F6'},
        {'code': 'WP4', 'name': 'Dissemination & Reporting', 'start': 1, 'end': 24, 'color': '#42A5F5'},
    ]
    
    # Create table-like structure
    y_positions = np.arange(len(work_packages))
    
    # Draw work package bars
    for i, wp in enumerate(work_packages):
        # Main bar
        rect = Rectangle((wp['start']-1, i-0.3), wp['end']-wp['start']+1, 0.6, 
                        facecolor=wp['color'], edgecolor='black', linewidth=1)
        ax.add_patch(rect)
        
        # Add work package name
        ax.text(-0.5, i, wp['name'], ha='right', va='center', 
               fontsize=11, fontweight='bold')
    
    # Add deliverable markers
    deliverables = [
        {'month': 3, 'name': 'D0.1'},
        {'month': 6, 'name': 'D0.2'},
        {'month': 12, 'name': 'D1.1'},
        {'month': 18, 'name': 'D2.1'},
        {'month': 22, 'name': 'D3.1'},
        {'month': 24, 'name': 'D4.1'}
    ]
    
    for deliv in deliverables:
        # Add vertical line for deliverable
        ax.axvline(x=deliv['month'], color='red', linestyle='-', alpha=0.8, linewidth=2)
        
        # Add deliverable label at top
        ax.text(deliv['month'], len(work_packages), deliv['name'], 
               ha='center', va='bottom', fontsize=10, fontweight='bold', color='red',
               bbox=dict(boxstyle='round,pad=0.2', facecolor='white', edgecolor='red'))
    
    # Customize the chart
    ax.set_xlim(-8, 25)
    ax.set_ylim(-0.5, len(work_packages) + 1)
    
    # Set labels and title
    ax.set_xlabel('Project Timeline (Months)', fontsize=14, fontweight='bold')
    ax.set_title('MJO Stratospheric Gravity Waves Project - 24 Month Timeline', 
                fontsize=16, fontweight='bold', pad=20)
    
    # Remove y-axis labels and ticks
    ax.set_yticks([])
    
    # Set x-axis ticks for months
    months = np.arange(1, 25)
    ax.set_xticks(months)
    ax.set_xticklabels([f'{m}' for m in months], fontsize=10)
    
    # Add grid
    ax.grid(True, axis='x', alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # Add quarter markers
    quarters = [6, 12, 18, 24]
    for q in quarters:
        ax.axvline(x=q, color='blue', linestyle='--', alpha=0.5, linewidth=1)
    
    # Remove top and right spines
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_visible(False)
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the chart
    plt.savefig('MJO_Simple_Gantt_Chart.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('MJO_Simple_Gantt_Chart.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print("Simple Gantt chart saved as 'MJO_Simple_Gantt_Chart.png' and 'MJO_Simple_Gantt_Chart.pdf'")
    
    return fig, ax

if __name__ == "__main__":
    # Create both chart styles
    print("Creating academic-style Gantt chart...")
    fig1, ax1 = create_academic_gantt_chart()
    
    print("Creating simple table-style Gantt chart...")
    fig2, ax2 = create_simple_table_gantt()
    
    # Display the charts
    plt.show()
    
    print("\nBoth Gantt chart styles have been created!")
    print("Files generated:")
    print("- MJO_Academic_Gantt_Chart.png/pdf")
    print("- MJO_Simple_Gantt_Chart.png/pdf")
